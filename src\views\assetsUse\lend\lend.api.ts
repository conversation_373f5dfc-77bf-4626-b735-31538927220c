import { defHttp } from '/@/utils/http/axios';
import { ApiPrefix } from '/@/settings/globalSetting';

enum Api {
  LendList = '/biz/loan/page',
  LendAdd = '/biz/loan/add',
  LendEdit = '/biz/loan/edit',
  LendDelete = '/biz/loan/delete',
  LendDetail = '/biz/loan/detail',
  LendExport = '/biz/loan/excel/export',
  LendExportAll = '/biz/loan/excel/exportAll',
  LendImport = '/biz/loan/excel/import',
  LendTemplate = '/biz/loan/template/excel/download',
}

/**
 * 获取借出信息列表
 */
export const list = (params: any) => {
  return defHttp.post({ url: Api.LendList, params });
};

/**
 * 保存或更新借出信息
 */
export const saveOrUpdate = (params: any, isUpdate: boolean) => {
  const url = isUpdate ? Api.LendEdit : Api.LendAdd;
  return defHttp.post({ url, params });
};

/**
 * 删除借出信息
 */
export const deleteLend = (params: any) => {
  return defHttp.delete({ url: Api.LendDelete, params });
};

/**
 * 获取借出信息详情
 */
export const getDetail = (id: string) => {
  return defHttp.get({ url: Api.LendDetail, params: { id } });
};

/**
 * 导出借出信息
 */
export const exportLend = (params: any) => {
  return defHttp.get({ url: Api.LendExport, params, responseType: 'blob' });
};

/**
 * 导出全部借出信息
 */
export const exportAllLend = (params: any) => {
  return defHttp.get({ url: Api.LendExportAll, params, responseType: 'blob' });
};

/**
 * 导入借出信息
 */
export const importLend = (params: any) => {
  return defHttp.post({ url: Api.LendImport, params });
};

/**
 * 下载导入模板
 */
export const downloadTemplate = () => {
  return defHttp.get({ url: Api.LendTemplate, responseType: 'blob' });
}; 